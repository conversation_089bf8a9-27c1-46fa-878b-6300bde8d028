// ==UserScript==
// @name         Amazon翻页健壮性测试器
// @namespace    http://tampermonkey.net/
// @version      1.0
// @description  测试Amazon翻页的边界条件和健壮性
// <AUTHOR>
// @include      *amazon*
// @exclude      *amazon*checkout*
// @exclude      *amazon*cart*
// @exclude      *amazon*account*
// @grant        none
// ==/UserScript==

(function() {
    'use strict';

    // 检查是否为评论页面
    function isReviewPage() {
        const url = location.href;
        return url.includes('product-reviews') || 
               url.includes('/reviews/') ||
               url.includes('customer-reviews');
    }

    if (!isReviewPage()) {
        return;
    }

    console.log('🧪 Amazon翻页健壮性测试器已加载');

    // 检查是否为最后一页
    function isLastPage() {
        // 方法1：检查页面信息
        const pageInfo = document.querySelector('[data-hook="cr-filter-info-review-rating-count"]');
        if (pageInfo) {
            const text = pageInfo.textContent;
            console.log('📊 页面信息:', text);
            const match = text.match(/(\d+)\s*-\s*(\d+)\s*of\s*(\d+)/);
            if (match) {
                const [, start, end, total] = match.map(Number);
                console.log(`📊 解析结果: ${start}-${end} of ${total}`);
                if (end >= total) {
                    console.log('✅ 方法1判断：已到达最后一页');
                    return true;
                }
            }
        }

        // 方法2：检查是否存在置灰的下一页按钮
        const disabledNextButtons = document.querySelectorAll('li.a-last.a-disabled, .a-pagination .a-last.a-disabled, [aria-label*="Next"].a-disabled');
        if (disabledNextButtons.length > 0) {
            console.log('✅ 方法2判断：找到置灰的下一页按钮，已到达最后一页');
            return true;
        }

        // 方法3：检查是否只有一页
        const paginationElements = document.querySelectorAll('.a-pagination li, .a-pagination a');
        console.log(`📊 分页元素数量: ${paginationElements.length}`);
        if (paginationElements.length <= 2) {
            console.log('✅ 方法3判断：分页元素少，可能只有一页');
            return true;
        }

        console.log('❌ 所有方法都判断不是最后一页');
        return false;
    }

    // 检查按钮是否被禁用
    function isButtonDisabled(button) {
        const disabled = button.classList.contains('a-disabled') || 
                        button.parentElement?.classList.contains('a-disabled') ||
                        button.getAttribute('aria-disabled') === 'true' ||
                        button.style.pointerEvents === 'none';
        
        console.log(`🔍 按钮禁用检查: ${disabled ? '已禁用' : '可用'}`);
        return disabled;
    }

    // 从URL中提取页码
    function extractPageFromURL(url) {
        try {
            const urlObj = new URL(url);
            const pageNumber = urlObj.searchParams.get('pageNumber');
            const result = pageNumber ? parseInt(pageNumber, 10) : null;
            console.log(`🔍 从URL提取页码: ${url} -> ${result}`);
            return result;
        } catch (error) {
            console.log(`❌ URL解析失败: ${error.message}`);
            return null;
        }
    }

    // 获取当前页码
    function getCurrentPage() {
        const urlPageNumber = new URL(location.href).searchParams.get('pageNumber');
        return urlPageNumber ? parseInt(urlPageNumber, 10) : 1;
    }

    // 查找下一页按钮
    function findNextPageButton() {
        const currentPage = getCurrentPage();
        console.log(`📄 当前页码: ${currentPage}`);

        // 首先检查是否已经是最后一页
        if (isLastPage()) {
            console.log('📄 已到达最后一页，没有下一页按钮');
            return null;
        }

        // 查找包含"Next page"文本的链接
        const allLinks = document.querySelectorAll('a[href*="pageNumber"]');
        console.log(`🔍 找到 ${allLinks.length} 个包含pageNumber的链接`);

        for (const link of allLinks) {
            const linkText = link.textContent.trim().toLowerCase();
            console.log(`🔗 检查链接: "${linkText}" -> ${link.href}`);
            
            if (linkText.includes('next page') || linkText.includes('next')) {
                // 验证按钮是否有效
                if (isButtonDisabled(link)) {
                    console.log(`⚠️ "Next page"按钮已置灰，无法点击`);
                    return null;
                }
                
                // 验证按钮指向的页码
                const targetPage = extractPageFromURL(link.href);
                if (targetPage && targetPage > currentPage) {
                    console.log(`✅ 找到有效的"Next page"按钮，指向第${targetPage}页`);
                    return link;
                } else {
                    console.log(`⚠️ "Next page"按钮指向页码不正确: 当前${currentPage}页，按钮指向${targetPage}页`);
                }
            }
        }

        console.log('❌ 未找到有效的下一页按钮');
        return null;
    }

    // 分析分页结构
    function analyzePagination() {
        console.log('\n🔍 分析分页结构:');
        
        // 查找所有分页相关元素
        const paginationContainer = document.querySelector('.a-pagination');
        if (paginationContainer) {
            console.log('✅ 找到分页容器');
            const items = paginationContainer.querySelectorAll('li, a');
            console.log(`📊 分页项目数量: ${items.length}`);
            
            items.forEach((item, index) => {
                const text = item.textContent.trim();
                const href = item.href || '无链接';
                const classes = item.className;
                console.log(`  ${index + 1}. "${text}" | ${href} | ${classes}`);
            });
        } else {
            console.log('❌ 未找到分页容器');
        }
        
        // 查找所有可能的下一页元素
        const nextElements = document.querySelectorAll('[aria-label*="Next"], [aria-label*="next"], a:contains("Next"), a:contains("next")');
        console.log(`📊 找到 ${nextElements.length} 个可能的下一页元素`);
    }

    function createTestPanel() {
        const panel = document.createElement('div');
        panel.style.cssText = `
            position: fixed;
            top: 10px;
            left: 10px;
            z-index: 10000;
            background: white;
            border: 2px solid #ff6600;
            border-radius: 8px;
            padding: 15px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.3);
            max-width: 300px;
            font-family: Arial, sans-serif;
            font-size: 12px;
        `;
        
        panel.innerHTML = `
            <div style="font-weight: bold; margin-bottom: 10px; color: #ff6600; font-size: 14px;">
                🧪 翻页健壮性测试
            </div>
            <button id="test-last-page" style="display: block; width: 100%; margin-bottom: 5px; padding: 8px; background: #4CAF50; color: white; border: none; border-radius: 4px; cursor: pointer;">
                检测是否最后一页
            </button>
            <button id="test-find-next" style="display: block; width: 100%; margin-bottom: 5px; padding: 8px; background: #2196F3; color: white; border: none; border-radius: 4px; cursor: pointer;">
                查找下一页按钮
            </button>
            <button id="test-analyze" style="display: block; width: 100%; margin-bottom: 5px; padding: 8px; background: #FF9800; color: white; border: none; border-radius: 4px; cursor: pointer;">
                分析分页结构
            </button>
            <button id="test-comprehensive" style="display: block; width: 100%; padding: 8px; background: #9C27B0; color: white; border: none; border-radius: 4px; cursor: pointer;">
                综合测试
            </button>
        `;
        
        document.body.appendChild(panel);
        
        // 绑定事件
        document.getElementById('test-last-page').onclick = () => {
            console.log('\n🧪 测试：检测是否最后一页');
            const result = isLastPage();
            alert(`是否最后一页: ${result ? '是' : '否'}\n详情请查看控制台`);
        };
        
        document.getElementById('test-find-next').onclick = () => {
            console.log('\n🧪 测试：查找下一页按钮');
            const button = findNextPageButton();
            if (button) {
                alert(`找到下一页按钮!\n文本: ${button.textContent.trim()}\n链接: ${button.href}`);
            } else {
                alert('未找到有效的下一页按钮');
            }
        };
        
        document.getElementById('test-analyze').onclick = () => {
            console.log('\n🧪 测试：分析分页结构');
            analyzePagination();
            alert('分页结构分析完成，详情请查看控制台');
        };
        
        document.getElementById('test-comprehensive').onclick = () => {
            console.log('\n🧪 综合测试开始');
            console.log('当前页码:', getCurrentPage());
            console.log('是否最后一页:', isLastPage());
            const nextButton = findNextPageButton();
            console.log('下一页按钮:', nextButton ? '找到' : '未找到');
            analyzePagination();
            alert('综合测试完成，详情请查看控制台');
        };
    }
    
    // 页面加载完成后创建测试面板
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', createTestPanel);
    } else {
        createTestPanel();
    }
    
})();
