# Amazon评论采集器URL匹配问题修复说明

## 问题描述

当脚本自动翻页跳转到包含完整产品名称的URL时，油猴脚本会消失，例如：
```
https://www.amazon.nl/SwitchBot-Air-Purifier-With-App-Control-And-Night-Light-CADR-400m³/h-Up-To-83m²-HEPA-Filter-Removes-99.97%-Mildew-Dust-Pollen-20dB-Sleep-Mode-Pet-Mode-For-Families/product-reviews/B0DG5QLWP6/ref=cm_cr_getr_d_paging_btm_2?ie=UTF8&filterByStar=four_star&pageNumber=2&reviewerType=all_reviews&pageSize=10
```

## 根本原因

Tampermonkey的`@match`规则无法有效匹配包含复杂产品名称路径的Amazon URL，因为：
1. 产品名称路径可能包含特殊字符和多个层级
2. URL结构在不同Amazon站点可能有差异
3. `@match`规则的通配符支持有限

## 修复方案

### 1. 使用更宽泛的匹配规则
将复杂的`@match`规则替换为简单的`@include`规则：

```javascript
// 原来的复杂匹配规则
// @match        *://www.amazon.*/product-reviews/*
// @match        *://www.amazon.*/*product-reviews*
// ... 多个复杂规则

// 新的简单匹配规则
// @include      *amazon*
// @exclude      *amazon*checkout*
// @exclude      *amazon*cart*
// @exclude      *amazon*account*
```

### 2. 添加页面检测逻辑
在脚本开始时检查是否为评论页面：

```javascript
function isReviewPage() {
  const url = location.href;
  const isReviewURL = url.includes('product-reviews') || 
                     url.includes('/reviews/') ||
                     url.includes('customer-reviews');
  
  const hasReviewElements = document.querySelector('[data-hook="review"]') ||
                           document.querySelector('.review') ||
                           document.querySelector('[id*="customer_review"]') ||
                           document.querySelector('.a-section.review');
  
  return isReviewURL || hasReviewElements;
}

// 如果不是评论页面，不加载脚本
if (!isReviewPage()) {
  console.log('🚫 当前页面不是Amazon评论页面，脚本未加载');
  return;
}
```

### 3. 优势

**新方案的优势：**
- ✅ 匹配所有Amazon域名和URL格式
- ✅ 自动排除购物车、结账等不相关页面
- ✅ 通过代码逻辑而非URL规则判断页面类型
- ✅ 更加灵活和可靠
- ✅ 支持未来的URL格式变化

**安全性：**
- ✅ 只在检测到评论元素的页面运行
- ✅ 排除敏感页面（结账、账户等）
- ✅ 不会在非Amazon页面运行

## 测试工具

### 1. URL匹配测试器 (`amazon_url_matcher_test.user.js`)
- 显示脚本是否成功匹配当前页面
- 分析URL结构和组成部分
- 测试不同URL格式的匹配情况

### 2. 通用版采集器 (`amazon_review_scraper_universal.user.js`)
- 使用新的匹配规则的完整版本
- 包含页面检测逻辑
- 可以作为主脚本的替代方案

## 使用建议

### 1. 立即修复
使用更新后的主脚本，它现在使用：
- `@include *amazon*` 匹配所有Amazon页面
- 页面检测逻辑确保只在评论页面运行
- 排除规则避免在不相关页面运行

### 2. 测试验证
1. 安装URL匹配测试器，确认脚本能在所有评论页面正常加载
2. 测试自动翻页功能，确认脚本不会在翻页后消失
3. 验证在非评论页面脚本不会加载

### 3. 预期效果
- ✅ 脚本在所有Amazon评论页面都能正常加载
- ✅ 自动翻页后脚本继续运行
- ✅ 不会在购物车、结账等页面意外运行
- ✅ 支持所有Amazon国际站点

## 技术细节

### 匹配规则对比
```javascript
// 旧规则（复杂但不完整）
// @match *://www.amazon.*/product-reviews/*

// 新规则（简单但全面）
// @include *amazon*
// @exclude *amazon*checkout*
```

### 页面检测逻辑
脚本会检查：
1. URL是否包含评论相关关键词
2. 页面是否包含评论相关DOM元素
3. 两个条件满足其一即认为是评论页面

这种方法比单纯依赖URL匹配更加可靠和灵活。
