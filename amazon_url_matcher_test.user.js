// ==UserScript==
// @name         Amazon URL匹配测试器
// @namespace    http://tampermonkey.net/
// @version      1.0
// @description  测试Amazon评论页面URL匹配规则
// <AUTHOR>
// @include      /^https?:\/\/www\.amazon\.[^\/]+\/.*product-reviews.*$/
// @match        *://www.amazon.*/product-reviews/*
// @match        *://www.amazon.*/*product-reviews*
// @match        *://www.amazon.*/dp/*/ref=*
// @match        *://amazon.*/product-reviews/*
// @match        *://*.amazon.*/product-reviews/*
// @grant        none
// ==/UserScript==

(function() {
    'use strict';

    console.log('🎯 Amazon URL匹配测试器已加载');
    console.log('📍 当前URL:', location.href);
    
    // 创建一个显眼的指示器
    function createIndicator() {
        const indicator = document.createElement('div');
        indicator.innerHTML = '✅ 脚本已匹配此页面';
        indicator.style.cssText = `
            position: fixed;
            top: 0;
            left: 50%;
            transform: translateX(-50%);
            z-index: 999999;
            padding: 10px 20px;
            background: #4CAF50;
            color: white;
            font-weight: bold;
            font-size: 14px;
            border-radius: 0 0 10px 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.3);
            animation: slideDown 0.5s ease-out;
        `;
        
        // 添加动画样式
        const style = document.createElement('style');
        style.textContent = `
            @keyframes slideDown {
                from { transform: translateX(-50%) translateY(-100%); }
                to { transform: translateX(-50%) translateY(0); }
            }
        `;
        document.head.appendChild(style);
        document.body.appendChild(indicator);
        
        // 5秒后自动隐藏
        setTimeout(() => {
            indicator.style.animation = 'slideDown 0.5s ease-out reverse';
            setTimeout(() => indicator.remove(), 500);
        }, 5000);
    }
    
    // 分析当前URL
    function analyzeURL() {
        const url = location.href;
        console.log('🔍 URL分析:');
        console.log('- 完整URL:', url);
        console.log('- 域名:', location.hostname);
        console.log('- 路径:', location.pathname);
        console.log('- 查询参数:', location.search);
        console.log('- 锚点:', location.hash);
        
        // 检查是否包含product-reviews
        if (url.includes('product-reviews')) {
            console.log('✅ URL包含"product-reviews"');
        } else {
            console.log('❌ URL不包含"product-reviews"');
        }
        
        // 检查ASIN
        const asinMatch = url.match(/\/product-reviews\/([A-Z0-9]{10})/);
        if (asinMatch) {
            console.log('✅ 找到ASIN:', asinMatch[1]);
        } else {
            console.log('❌ 未找到ASIN');
        }
        
        // 检查页码
        const pageMatch = url.match(/pageNumber=(\d+)/);
        if (pageMatch) {
            console.log('✅ 找到页码:', pageMatch[1]);
        } else {
            console.log('❌ 未找到页码');
        }
    }
    
    // 测试URL匹配规则
    function testURLPatterns() {
        const testURLs = [
            'https://www.amazon.nl/product-reviews/B0DG5QLWP6/',
            'https://www.amazon.nl/SwitchBot-Air-Purifier-With-App-Control/product-reviews/B0DG5QLWP6/',
            'https://www.amazon.com/product-reviews/B0DG5QLWP6/',
            'https://www.amazon.de/product-reviews/B0DG5QLWP6/',
            location.href
        ];
        
        console.log('🧪 测试URL匹配规则:');
        const pattern = /^https?:\/\/www\.amazon\.[^\/]+\/.*product-reviews.*$/;
        
        testURLs.forEach(url => {
            const matches = pattern.test(url);
            console.log(`${matches ? '✅' : '❌'} ${url}`);
        });
    }
    
    // 页面加载完成后执行
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => {
            createIndicator();
            analyzeURL();
            testURLPatterns();
        });
    } else {
        createIndicator();
        analyzeURL();
        testURLPatterns();
    }
    
    // 监听URL变化（用于SPA应用）
    let lastURL = location.href;
    new MutationObserver(() => {
        const currentURL = location.href;
        if (currentURL !== lastURL) {
            lastURL = currentURL;
            console.log('🔄 URL已变化:', currentURL);
            analyzeURL();
        }
    }).observe(document, { subtree: true, childList: true });
    
})();
