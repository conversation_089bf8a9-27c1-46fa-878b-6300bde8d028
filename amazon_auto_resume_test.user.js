// ==UserScript==
// @name         Amazon自动模式恢复测试器
// @namespace    http://tampermonkey.net/
// @version      1.0
// @description  测试Amazon评论采集器的自动模式恢复功能
// <AUTHOR>
// @include      *amazon*
// @exclude      *amazon*checkout*
// @exclude      *amazon*cart*
// @exclude      *amazon*account*
// @grant        none
// ==/UserScript==

(function() {
    'use strict';

    // 检查是否为评论页面
    function isReviewPage() {
        const url = location.href;
        return url.includes('product-reviews') || 
               url.includes('/reviews/') ||
               url.includes('customer-reviews');
    }

    if (!isReviewPage()) {
        return;
    }

    console.log('🧪 Amazon自动模式恢复测试器已加载');

    function checkLocalStorageState() {
        console.log('\n🔍 检查localStorage状态:');
        const keys = [
            'amazonReviewScraperAutoMode',
            'amazonReviewScraperTotalPages', 
            'amazonReviewScraperMaxPages',
            'amazonReviewScraperClickTime'
        ];
        
        keys.forEach(key => {
            const value = localStorage.getItem(key);
            console.log(`  ${key}: ${value}`);
        });
        
        const clickTime = parseInt(localStorage.getItem('amazonReviewScraperClickTime') || '0', 10);
        if (clickTime) {
            const timeDiff = Date.now() - clickTime;
            console.log(`  点击时间差: ${Math.round(timeDiff/1000)}秒前`);
        }
    }

    function simulateAutoModeState() {
        console.log('\n🔧 模拟自动模式状态...');
        localStorage.setItem('amazonReviewScraperAutoMode', 'true');
        localStorage.setItem('amazonReviewScraperTotalPages', '1');
        localStorage.setItem('amazonReviewScraperMaxPages', '20');
        localStorage.setItem('amazonReviewScraperClickTime', Date.now().toString());
        console.log('✅ 已设置模拟状态');
        checkLocalStorageState();
    }

    function clearAutoModeState() {
        console.log('\n🧹 清除自动模式状态...');
        localStorage.removeItem('amazonReviewScraperAutoMode');
        localStorage.removeItem('amazonReviewScraperTotalPages');
        localStorage.removeItem('amazonReviewScraperMaxPages');
        localStorage.removeItem('amazonReviewScraperClickTime');
        console.log('✅ 已清除状态');
        checkLocalStorageState();
    }

    function testAutoModeResume() {
        console.log('\n🧪 测试自动模式恢复逻辑...');
        
        const shouldResume = localStorage.getItem('amazonReviewScraperAutoMode');
        const savedTotalPages = parseInt(localStorage.getItem('amazonReviewScraperTotalPages') || '0', 10);
        const savedMaxPages = parseInt(localStorage.getItem('amazonReviewScraperMaxPages') || '20', 10);
        const clickTime = parseInt(localStorage.getItem('amazonReviewScraperClickTime') || '0', 10);
        
        console.log(`📊 shouldResume: ${shouldResume}`);
        console.log(`📊 savedTotalPages: ${savedTotalPages}`);
        console.log(`📊 savedMaxPages: ${savedMaxPages}`);
        console.log(`📊 clickTime: ${clickTime}`);
        
        if (shouldResume === 'true') {
            const now = Date.now();
            const timeDiff = now - clickTime;
            console.log(`⏱️ 时间差: ${timeDiff}ms (${Math.round(timeDiff/1000)}秒)`);
            
            if (clickTime && timeDiff > 60000) {
                console.log('⚠️ 检测到超时，应该清除状态');
                return 'timeout';
            } else {
                console.log('✅ 应该恢复自动模式');
                return 'resume';
            }
        } else {
            console.log('ℹ️ 无需恢复');
            return 'no_resume';
        }
    }

    function createTestPanel() {
        const panel = document.createElement('div');
        panel.style.cssText = `
            position: fixed;
            top: 10px;
            right: 10px;
            z-index: 10000;
            background: white;
            border: 2px solid #ff6600;
            border-radius: 8px;
            padding: 15px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.3);
            max-width: 250px;
            font-family: Arial, sans-serif;
            font-size: 12px;
        `;
        
        panel.innerHTML = `
            <div style="font-weight: bold; margin-bottom: 10px; color: #ff6600; font-size: 14px;">
                🧪 自动模式恢复测试
            </div>
            <button id="check-state" style="display: block; width: 100%; margin-bottom: 5px; padding: 8px; background: #4CAF50; color: white; border: none; border-radius: 4px; cursor: pointer;">
                检查localStorage状态
            </button>
            <button id="simulate-state" style="display: block; width: 100%; margin-bottom: 5px; padding: 8px; background: #2196F3; color: white; border: none; border-radius: 4px; cursor: pointer;">
                模拟自动模式状态
            </button>
            <button id="test-resume" style="display: block; width: 100%; margin-bottom: 5px; padding: 8px; background: #FF9800; color: white; border: none; border-radius: 4px; cursor: pointer;">
                测试恢复逻辑
            </button>
            <button id="clear-state" style="display: block; width: 100%; padding: 8px; background: #f44336; color: white; border: none; border-radius: 4px; cursor: pointer;">
                清除状态
            </button>
        `;
        
        document.body.appendChild(panel);
        
        // 绑定事件
        document.getElementById('check-state').onclick = () => {
            checkLocalStorageState();
            alert('localStorage状态已输出到控制台');
        };
        
        document.getElementById('simulate-state').onclick = () => {
            simulateAutoModeState();
            alert('已模拟自动模式状态，详情请查看控制台');
        };
        
        document.getElementById('test-resume').onclick = () => {
            const result = testAutoModeResume();
            alert(`恢复逻辑测试结果: ${result}\n详情请查看控制台`);
        };
        
        document.getElementById('clear-state').onclick = () => {
            clearAutoModeState();
            alert('已清除自动模式状态');
        };
    }
    
    // 页面加载时自动检查状态
    function autoCheck() {
        console.log('\n🚀 页面加载，自动检查状态...');
        checkLocalStorageState();
        
        const shouldResume = localStorage.getItem('amazonReviewScraperAutoMode');
        if (shouldResume === 'true') {
            console.log('🔔 检测到需要恢复的自动模式状态！');
            const result = testAutoModeResume();
            console.log(`🎯 恢复逻辑结果: ${result}`);
        }
    }
    
    // 页面加载完成后创建测试面板和自动检查
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => {
            createTestPanel();
            autoCheck();
        });
    } else {
        createTestPanel();
        autoCheck();
    }
    
})();
