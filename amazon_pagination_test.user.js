// ==UserScript==
// @name         Amazon翻页测试器
// @namespace    http://tampermonkey.net/
// @version      1.0
// @description  测试Amazon评论页面的翻页逻辑
// <AUTHOR>
// @match        https://www.amazon.*/product-reviews/*
// @match        *://www.amazon.*/*/product-reviews/*
// @grant        none
// ==/UserScript==

(function() {
    'use strict';

    function testPagination() {
        console.log('🧪 开始测试翻页逻辑...');
        console.log('📍 当前URL:', location.href);
        
        // 查找所有可能的下一页链接
        const allLinks = document.querySelectorAll('a');
        const pageLinks = [];
        
        allLinks.forEach((link, index) => {
            const href = link.href;
            const text = link.textContent.trim();
            
            if (href.includes('pageNumber') || 
                text.toLowerCase().includes('next') || 
                text.includes('→') ||
                link.getAttribute('aria-label')?.toLowerCase().includes('next')) {
                
                pageLinks.push({
                    index,
                    text: text,
                    href: href,
                    ariaLabel: link.getAttribute('aria-label'),
                    className: link.className
                });
            }
        });
        
        console.log(`🔗 找到 ${pageLinks.length} 个可能的翻页链接:`);
        pageLinks.forEach((link, i) => {
            console.log(`${i + 1}. 文本: "${link.text}"`);
            console.log(`   链接: ${link.href}`);
            console.log(`   aria-label: ${link.ariaLabel}`);
            console.log(`   class: ${link.className}`);
            console.log('---');
        });
        
        // 测试URL构造
        const url = new URL(location.href);
        const current = parseInt(url.searchParams.get('pageNumber') || '1', 10);
        const nextPage = current + 1;
        
        console.log(`📄 当前页: ${current}, 下一页: ${nextPage}`);
        
        // 尝试构造URL
        const pathMatch = url.pathname.match(/\/product-reviews\/([A-Z0-9]{10})/);
        if (pathMatch) {
            const asin = pathMatch[1];
            console.log(`🏷️ 产品ASIN: ${asin}`);
            
            const baseUrl = `${url.origin}/product-reviews/${asin}/ref=cm_cr_getr_d_paging_btm_next_${nextPage}`;
            const params = new URLSearchParams();
            params.set('ie', 'UTF8');
            if (url.searchParams.get('filterByStar')) {
                params.set('filterByStar', url.searchParams.get('filterByStar'));
            }
            params.set('reviewerType', url.searchParams.get('reviewerType') || 'all_reviews');
            params.set('pageNumber', nextPage.toString());
            
            const constructedUrl = `${baseUrl}?${params.toString()}#reviews-filter-bar`;
            console.log(`🔧 构造的下一页URL: ${constructedUrl}`);
        }
        
        return pageLinks;
    }
    
    // 创建测试按钮
    function createTestButton() {
        const button = document.createElement('button');
        button.textContent = '🧪 测试翻页';
        button.style.cssText = `
            position: fixed;
            top: 50px;
            right: 10px;
            z-index: 10000;
            padding: 10px;
            background: #ff6600;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-weight: bold;
        `;
        
        button.onclick = () => {
            const links = testPagination();
            alert(`找到 ${links.length} 个翻页链接，详情请查看控制台`);
        };
        
        document.body.appendChild(button);
    }
    
    // 页面加载完成后创建测试按钮
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', createTestButton);
    } else {
        createTestButton();
    }
    
    console.log('🚀 Amazon翻页测试脚本已加载');
})();
