// ==UserScript==
// @name         Amazon点击测试器
// @namespace    http://tampermonkey.net/
// @version      1.0
// @description  测试Amazon下一页按钮的点击行为
// <AUTHOR>
// @include      *amazon*
// @exclude      *amazon*checkout*
// @exclude      *amazon*cart*
// @exclude      *amazon*account*
// @grant        none
// ==/UserScript==

(function() {
    'use strict';

    // 检查是否为评论页面
    function isReviewPage() {
        const url = location.href;
        return url.includes('product-reviews') || 
               url.includes('/reviews/') ||
               url.includes('customer-reviews');
    }

    if (!isReviewPage()) {
        return;
    }

    console.log('🧪 Amazon点击测试器已加载');

    function findNextPageButton() {
        // 查找所有可能的下一页按钮
        const allLinks = document.querySelectorAll('a[href*="pageNumber"]');
        console.log(`🔍 找到 ${allLinks.length} 个包含pageNumber的链接`);
        
        for (const link of allLinks) {
            const linkText = link.textContent.trim().toLowerCase();
            console.log(`🔗 检查链接: "${linkText}" -> ${link.href}`);
            if (linkText.includes('next page') || linkText.includes('next')) {
                console.log(`✅ 找到"Next page"按钮`);
                return link;
            }
        }
        
        // 使用传统选择器
        const nextSelectors = [
            'li.a-last a:not(.a-disabled)',
            '[aria-label*="Next"]:not(.a-disabled)',
            '.a-pagination .a-last a:not(.a-disabled)',
            'a[aria-label*="next page"]'
        ];

        for (const selector of nextSelectors) {
            const nextButton = document.querySelector(selector);
            if (nextButton && nextButton.href) {
                console.log(`🔗 找到下一页按钮: ${selector}`);
                return nextButton;
            }
        }
        
        return null;
    }

    function testDirectURLChange() {
        const currentURL = new URL(location.href);
        const currentPage = parseInt(currentURL.searchParams.get('pageNumber') || '1', 10);
        const nextPage = currentPage + 1;
        
        currentURL.searchParams.set('pageNumber', nextPage.toString());
        const newURL = currentURL.toString();
        
        console.log(`🔗 当前页: ${currentPage}`);
        console.log(`🔗 构造的下一页URL: ${newURL}`);
        
        return newURL;
    }

    function createTestButtons() {
        const container = document.createElement('div');
        container.style.cssText = `
            position: fixed;
            top: 10px;
            right: 10px;
            z-index: 10000;
            background: white;
            border: 2px solid #ff6600;
            border-radius: 8px;
            padding: 10px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.3);
        `;
        
        container.innerHTML = `
            <div style="font-weight: bold; margin-bottom: 10px; color: #ff6600;">
                🧪 Amazon点击测试器
            </div>
            <button id="test-find-button" style="display: block; width: 100%; margin-bottom: 5px; padding: 8px; background: #4CAF50; color: white; border: none; border-radius: 4px; cursor: pointer;">
                查找下一页按钮
            </button>
            <button id="test-click-button" style="display: block; width: 100%; margin-bottom: 5px; padding: 8px; background: #2196F3; color: white; border: none; border-radius: 4px; cursor: pointer;">
                模拟点击下一页
            </button>
            <button id="test-url-change" style="display: block; width: 100%; margin-bottom: 5px; padding: 8px; background: #FF9800; color: white; border: none; border-radius: 4px; cursor: pointer;">
                测试URL直接跳转
            </button>
            <button id="test-compare" style="display: block; width: 100%; padding: 8px; background: #9C27B0; color: white; border: none; border-radius: 4px; cursor: pointer;">
                对比两种方法
            </button>
        `;
        
        document.body.appendChild(container);
        
        // 绑定事件
        document.getElementById('test-find-button').onclick = () => {
            const button = findNextPageButton();
            if (button) {
                console.log('✅ 找到下一页按钮:', button);
                console.log('📍 按钮文本:', button.textContent.trim());
                console.log('🔗 按钮链接:', button.href);
                alert(`找到下一页按钮！\n文本: ${button.textContent.trim()}\n链接: ${button.href}`);
            } else {
                console.log('❌ 未找到下一页按钮');
                alert('未找到下一页按钮');
            }
        };
        
        document.getElementById('test-click-button').onclick = () => {
            const button = findNextPageButton();
            if (button) {
                console.log('🖱️ 模拟点击下一页按钮...');
                alert('即将模拟点击下一页按钮，请观察页面变化');
                setTimeout(() => {
                    button.click();
                }, 1000);
            } else {
                alert('未找到下一页按钮');
            }
        };
        
        document.getElementById('test-url-change').onclick = () => {
            const newURL = testDirectURLChange();
            alert(`即将通过URL直接跳转到下一页\n新URL: ${newURL}`);
            setTimeout(() => {
                location.href = newURL;
            }, 1000);
        };
        
        document.getElementById('test-compare').onclick = () => {
            const button = findNextPageButton();
            const newURL = testDirectURLChange();
            
            const comparison = `
对比两种翻页方法：

方法1 - 模拟点击按钮:
${button ? `✅ 找到按钮: ${button.textContent.trim()}` : '❌ 未找到按钮'}
${button ? `🔗 按钮链接: ${button.href}` : ''}

方法2 - 直接URL跳转:
🔗 构造的URL: ${newURL}

建议: ${button ? '使用模拟点击方法' : '使用URL跳转方法'}
            `;
            
            alert(comparison);
            console.log(comparison);
        };
    }
    
    // 页面加载完成后创建测试按钮
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', createTestButtons);
    } else {
        createTestButtons();
    }
    
})();
