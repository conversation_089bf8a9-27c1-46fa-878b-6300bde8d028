// ==UserScript==
// @name         Amazon自动采集测试器
// @namespace    http://tampermonkey.net/
// @version      1.0
// @description  测试Amazon自动采集的完整流程
// <AUTHOR>
// @include      *amazon*
// @exclude      *amazon*checkout*
// @exclude      *amazon*cart*
// @exclude      *amazon*account*
// @grant        none
// ==/UserScript==

(function() {
    'use strict';

    // 检查是否为评论页面
    function isReviewPage() {
        const url = location.href;
        return url.includes('product-reviews') || 
               url.includes('/reviews/') ||
               url.includes('customer-reviews');
    }

    if (!isReviewPage()) {
        return;
    }

    console.log('🧪 Amazon自动采集测试器已加载');

    // 监控localStorage变化
    let lastAutoModeState = localStorage.getItem('amazonReviewScraperAutoMode');
    
    function monitorLocalStorage() {
        const currentState = localStorage.getItem('amazonReviewScraperAutoMode');
        if (currentState !== lastAutoModeState) {
            console.log(`📊 localStorage状态变化: ${lastAutoModeState} -> ${currentState}`);
            lastAutoModeState = currentState;
            
            if (currentState === 'true') {
                console.log('🔔 检测到自动模式状态被设置，开始监控恢复过程...');
                monitorAutoModeResume();
            }
        }
    }

    // 监控自动模式恢复过程
    function monitorAutoModeResume() {
        let checkCount = 0;
        const maxChecks = 15; // 最多检查15秒
        
        const resumeInterval = setInterval(() => {
            checkCount++;
            const shouldResume = localStorage.getItem('amazonReviewScraperAutoMode');
            
            console.log(`🔍 恢复监控${checkCount}: localStorage=${shouldResume}`);
            
            if (shouldResume === null) {
                console.log('✅ 自动模式状态已被清除，说明恢复逻辑已执行');
                clearInterval(resumeInterval);
            } else if (checkCount >= maxChecks) {
                console.log('⚠️ 监控超时，自动模式可能没有正确恢复');
                clearInterval(resumeInterval);
            }
        }, 1000);
    }

    // 检查页面是否有下一页按钮
    function checkNextPageButton() {
        const allLinks = document.querySelectorAll('a[href*="pageNumber"]');
        console.log(`🔍 找到 ${allLinks.length} 个包含pageNumber的链接`);
        
        for (const link of allLinks) {
            const linkText = link.textContent.trim().toLowerCase();
            if (linkText.includes('next page') || linkText.includes('next')) {
                const currentPage = getCurrentPage();
                const targetPage = extractPageFromURL(link.href);
                
                console.log(`🔗 下一页按钮: "${linkText}"`);
                console.log(`📄 当前页: ${currentPage}, 目标页: ${targetPage}`);
                console.log(`🔗 按钮链接: ${link.href}`);
                
                if (targetPage && targetPage > currentPage) {
                    console.log('✅ 下一页按钮有效');
                    return true;
                } else {
                    console.log('⚠️ 下一页按钮指向页码不正确');
                    return false;
                }
            }
        }
        
        console.log('❌ 未找到下一页按钮');
        return false;
    }

    // 获取当前页码
    function getCurrentPage() {
        const urlPageNumber = new URL(location.href).searchParams.get('pageNumber');
        return urlPageNumber ? parseInt(urlPageNumber, 10) : 1;
    }

    // 从URL中提取页码
    function extractPageFromURL(url) {
        try {
            const urlObj = new URL(url);
            const pageNumber = urlObj.searchParams.get('pageNumber');
            return pageNumber ? parseInt(pageNumber, 10) : null;
        } catch (error) {
            return null;
        }
    }

    // 检查评论数量
    function checkReviewCount() {
        const reviewSelectors = [
            'div[id*="customer_review"]',
            'div[data-hook="review"]',
            '.review'
        ];
        
        let totalReviews = 0;
        reviewSelectors.forEach(selector => {
            const reviews = document.querySelectorAll(selector);
            if (reviews.length > 0) {
                console.log(`📊 选择器 "${selector}" 找到 ${reviews.length} 个评论`);
                totalReviews = Math.max(totalReviews, reviews.length);
            }
        });
        
        return totalReviews;
    }

    // 创建状态显示面板
    function createStatusPanel() {
        const panel = document.createElement('div');
        panel.id = 'auto-collect-test-panel';
        panel.style.cssText = `
            position: fixed;
            top: 10px;
            left: 10px;
            z-index: 10000;
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            max-width: 300px;
        `;
        
        document.body.appendChild(panel);
        return panel;
    }

    // 更新状态显示
    function updateStatus() {
        const panel = document.getElementById('auto-collect-test-panel');
        if (!panel) return;
        
        const currentPage = getCurrentPage();
        const reviewCount = checkReviewCount();
        const hasNextButton = checkNextPageButton();
        const autoModeState = localStorage.getItem('amazonReviewScraperAutoMode');
        
        panel.innerHTML = `
            <div><strong>🧪 自动采集测试状态</strong></div>
            <div>📄 当前页: ${currentPage}</div>
            <div>📊 评论数: ${reviewCount}</div>
            <div>🔗 下一页按钮: ${hasNextButton ? '✅' : '❌'}</div>
            <div>🔄 自动模式: ${autoModeState || '无'}</div>
            <div><small>详情请查看控制台</small></div>
        `;
    }

    // 初始化
    function init() {
        console.log('\n🚀 自动采集测试器初始化');
        console.log(`📄 当前页码: ${getCurrentPage()}`);
        console.log(`📊 评论数量: ${checkReviewCount()}`);
        checkNextPageButton();
        
        createStatusPanel();
        updateStatus();
        
        // 定期监控localStorage和更新状态
        setInterval(() => {
            monitorLocalStorage();
            updateStatus();
        }, 1000);
        
        // 页面加载完成后的检查
        setTimeout(() => {
            console.log('\n🔍 页面加载完成后的状态检查:');
            const autoModeState = localStorage.getItem('amazonReviewScraperAutoMode');
            if (autoModeState === 'true') {
                console.log('🔔 发现需要恢复的自动模式状态！');
                monitorAutoModeResume();
            } else {
                console.log('ℹ️ 无自动模式状态需要恢复');
            }
        }, 2000);
    }

    // 页面加载完成后初始化
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init);
    } else {
        init();
    }
    
})();
