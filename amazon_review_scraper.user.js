// ==UserScript==
// @name         Amazon 评论采集器 (Review Scraper) Enhanced
// @namespace    https://yourdomain.example
// @version      1.1.0
// @description  采集亚马逊商品评论（用户名、星级、标题、内容等），支持自动翻页并导出 CSV / JSON。请遵守亚马逊条款与当地法律！
// <AUTHOR>
// @include      *amazon*
// @exclude      *amazon*checkout*
// @exclude      *amazon*cart*
// @exclude      *amazon*account*
// @icon         https://www.amazon.com/favicon.ico
// @grant        GM_setClipboard
// @grant        GM_setValue
// @grant        GM_getValue
// @license      MIT
// ==/UserScript==

(function () {
  'use strict';

  // 检查当前页面是否为评论页面
  function isReviewPage() {
    const url = location.href;
    const isReviewURL = url.includes('product-reviews') ||
                       url.includes('/reviews/') ||
                       url.includes('customer-reviews');

    const hasReviewElements = document.querySelector('[data-hook="review"]') ||
                             document.querySelector('.review') ||
                             document.querySelector('[id*="customer_review"]') ||
                             document.querySelector('.a-section.review');

    return isReviewURL || hasReviewElements;
  }

  // 如果不是评论页面，不加载脚本
  if (!isReviewPage()) {
    console.log('🚫 当前页面不是Amazon评论页面，脚本未加载');
    return;
  }

  console.log('✅ 检测到Amazon评论页面，加载采集器...');

  /**************** 配置区域（可按需调整） ****************/
  const CONFIG = {
    maxAutoPages: 20,           // 自动模式最大页数（从当前页开始计）
    delayBase: 2000,            // 翻页基础延迟(ms) - 增加延迟避免被检测
    delayJitter: 1500,          // 额外随机抖动(ms)
    logLimit: 1200,            // 面板日志最大行数
    parseDateToISO: true,       // 尝试将日期文本解析为 ISO8601
    maxRetries: 3,             // 最大重试次数
    stopOnCaptchaKeywords: ['automated access', 'captcha', 'unusual traffic', 'verify you are human', 'enter the characters you see below'],
  };

  /**************** 状态变量 ****************/
  let collected = [];           // 已采集评论对象数组
  let collectedIds = new Set(); // 去重集合
  let autoMode = false;         // 是否正在自动翻页中
  let currentPage = getQueryParam('pageNumber') ? parseInt(getQueryParam('pageNumber'), 10) : 1;
  let totalTriedPages = 0;
  let retryCount = 0;

  /**************** UI 创建 ****************/
  const panel = document.createElement('div');
  panel.style.cssText = `
    position:fixed; z-index:999999; right:12px; bottom:12px; width:340px;
    background:#1e2327; color:#fff; font-size:12px; font-family:Arial, sans-serif;
    border:1px solid #444; border-radius:8px; box-shadow:0 4px 16px rgba(0,0,0,.5); padding:12px;
    backdrop-filter: blur(10px);
  `;
  panel.innerHTML = `
    <div style="font-weight:bold; font-size:14px; margin-bottom:8px; color:#4CAF50;">
      🛒 Amazon 评论采集器 v1.1
    </div>
    <div style="font-size:11px; color:#aaa; margin-bottom:8px;">
      已采集: <span id="rc-count">0</span> 条 | 当前页: <span id="rc-page">${currentPage}</span>
      <button id="rc-btn-refresh-page" style="margin-left:8px; padding:2px 6px; border:none; border-radius:3px; background:#666; color:white; cursor:pointer; font-size:10px;">🔄</button>
    </div>
    <div style="display:flex; flex-wrap:wrap; gap:6px; margin-bottom:8px;">
      <button id="rc-btn-collect" style="flex:1; padding:6px; border:none; border-radius:4px; background:#2196F3; color:white; cursor:pointer;">采集当前页</button>
      <button id="rc-btn-auto" style="flex:1; padding:6px; border:none; border-radius:4px; background:#4CAF50; color:white; cursor:pointer;">自动采集</button>
      <button id="rc-btn-stop" style="flex:1; padding:6px; border:none; border-radius:4px; background:#f44336; color:white; cursor:pointer;">停止</button>
    </div>
    <div style="display:flex; gap:6px; margin-bottom:8px;">
      <button id="rc-btn-json" style="flex:1; padding:6px; border:none; border-radius:4px; background:#FF9800; color:white; cursor:pointer;">导出JSON</button>
      <button id="rc-btn-csv" style="flex:1; padding:6px; border:none; border-radius:4px; background:#9C27B0; color:white; cursor:pointer;">导出CSV</button>
      <button id="rc-btn-clear" style="flex:1; padding:6px; border:none; border-radius:4px; background:#607D8B; color:white; cursor:pointer;">清空</button>
    </div>
    <div style="font-size:11px; line-height:1.4; max-height:150px; overflow:auto; background:#111; padding:8px; border:1px solid #333; border-radius:4px;" id="rc-log"></div>
  `;
  document.body.appendChild(panel);

  // 获取UI元素
  const btnCollect = panel.querySelector('#rc-btn-collect');
  const btnAuto = panel.querySelector('#rc-btn-auto');
  const btnStop = panel.querySelector('#rc-btn-stop');
  const btnJson = panel.querySelector('#rc-btn-json');
  const btnCsv = panel.querySelector('#rc-btn-csv');
  const btnClear = panel.querySelector('#rc-btn-clear');
  const btnRefreshPage = panel.querySelector('#rc-btn-refresh-page');
  const logBox = panel.querySelector('#rc-log');
  const countSpan = panel.querySelector('#rc-count');
  const pageSpan = panel.querySelector('#rc-page');

  /**************** 事件绑定 ****************/
  btnCollect.addEventListener('click', () => {
    autoMode = false;
    log(`开始采集第 ${currentPage} 页（手动模式）...`);
    collectCurrentPageWithRetry();
  });

  btnAuto.addEventListener('click', () => {
    if (autoMode) {
      log('已经在自动模式中');
      return;
    }
    autoMode = true;
    totalTriedPages = 0;
    retryCount = 0;
    log(`🚀 自动采集启动。从第 ${currentPage} 页开始，最大页数限制：${CONFIG.maxAutoPages}`);
    autoLoop();
  });

  btnStop.addEventListener('click', () => {
    autoMode = false;
    log('⏹️ 已请求停止。');
  });

  btnRefreshPage.addEventListener('click', () => {
    updateCurrentPage();
    log('🔄 页码已刷新');
  });

  btnJson.addEventListener('click', () => {
    if (!collected.length) {
      log('❌ 当前没有数据可导出。');
      return;
    }
    const jsonStr = JSON.stringify(collected, null, 2);
    downloadFile(jsonStr, `amazon_reviews_${getTimestamp()}_${collected.length}.json`, 'application/json;charset=utf-8;');
  });

  btnCsv.addEventListener('click', () => {
    if (!collected.length) {
      log('❌ 当前没有数据可导出。');
      return;
    }
    const csvStr = toCSV(collected);
    const bom = "\uFEFF"; // BOM for Excel UTF-8 support
    downloadFile(bom + csvStr, `amazon_reviews_${getTimestamp()}_${collected.length}.csv`, 'text/csv;charset=utf-8;');
  });

  btnClear.addEventListener('click', () => {
    if (confirm('确定要清空所有已采集的数据吗？')) {
      collected = [];
      collectedIds.clear();
      updateUI();
      log('🗑️ 数据已清空。');
    }
  });

  /**************** 核心函数：采集页面 ****************/
  function collectCurrentPageWithRetry() {
    try {
      collectCurrentPage();
      retryCount = 0; // 重置重试计数
    } catch (error) {
      log(`❌ 采集出错: ${error.message}`);
      retryCount++;
      
      if (retryCount <= CONFIG.maxRetries) {
        log(`🔄 第 ${retryCount} 次重试...`);
        setTimeout(() => collectCurrentPageWithRetry(), 2000);
      } else {
        log('❌ 重试次数已达上限，停止采集');
        autoMode = false;
        retryCount = 0;
      }
    }
  }

  function collectCurrentPage() {
    // 更新当前页码
    updateCurrentPage();

    // 检测页面是否包含验证码或访问限制提示
    if (detectCaptcha()) {
      log('🚫 疑似触发亚马逊访问验证/机器人检测，已停止。');
      autoMode = false;
      return;
    }

    const reviewItems = getReviewItems();
    if (!reviewItems.length) {
      log('⚠️ 未检测到评论节点，可能页面结构已变或没有评论。');
      // 调试模式：显示页面中可能的评论容器
      debugPageStructure();
      return;
    }

    let countNew = 0;
    reviewItems.forEach(block => {
      const reviewData = extractReviewData(block);
      if (reviewData && !collectedIds.has(reviewData.reviewId)) {
        collected.push(reviewData);
        collectedIds.add(reviewData.reviewId);
        countNew++;
      }
    });

    updateUI();
    log(`✅ 第 ${currentPage} 页采集完成：新增 ${countNew} 条，总计 ${collected.length} 条。`);
  }

  // 更健壮的评论项获取
  function getReviewItems() {
    const selectors = [
      'div[data-hook="review"]',
      'div[id*="customer_review"]', // 新增：匹配 customer_review_foreign-xxx 格式的ID
      'div.a-section.celwidget[id*="customer_review"]', // 新增：更具体的选择器
      '[data-testid="reviews-section"] > div',
      '.review-item',
      '.a-section.review'
    ];

    for (const selector of selectors) {
      const items = document.querySelectorAll(selector);
      if (items.length > 0) {
        log(`📍 使用选择器: ${selector}, 找到 ${items.length} 个评论`);
        return items;
      }
    }
    return [];
  }

  // 提取评论数据
  function extractReviewData(block) {
    const reviewId = block.getAttribute('id') || block.getAttribute('data-review-id') || '';
    if (!reviewId) return null;

    const profileName = text(block.querySelector('.a-profile-name')) ||
                       text(block.querySelector('.a-profile-content .a-profile-name')) ||
                       text(block.querySelector('[data-hook="review-author"]'));

    // 星级解析
    const starData = getStarRating(block);
    
    // 标题与正文
    const title = text(block.querySelector('[data-hook="review-title"] span:not(.a-color-base)')) ||
                  text(block.querySelector('[data-hook="review-title"] span')) ||
                  text(block.querySelector('.review-title-content span')) ||
                  text(block.querySelector('.review-title'));
    
    const body = getReviewBody(block);

    // 日期
    const dateRaw = text(block.querySelector('[data-hook="review-date"]')) ||
                   text(block.querySelector('.review-date'));
    const dateISO = CONFIG.parseDateToISO ? tryParseDate(dateRaw) : '';

    // Verified Purchase
    const verified = !!block.querySelector('[data-hook="avp-badge"]') ||
                    !!block.querySelector('.a-color-success');

    // 图片数量
    const imgCount = block.querySelectorAll('.review-image-tile-section img, .review-image img').length;

    // helpful votes
    const helpfulRaw = text(block.querySelector('[data-hook="helpful-vote-statement"]'));
    const helpful = parseHelpful(helpfulRaw);

    return {
      reviewId,
      reviewer: profileName,
      star: starData.star,
      starText: starData.starText,
      title,
      dateRaw,
      dateISO,
      body,
      verified,
      imageCount: imgCount,
      helpful,
      pageNumber: currentPage,
      url: location.href,
      scrapedAt: new Date().toISOString()
    };
  }

  // 更健壮的星级获取
  function getStarRating(block) {
    const starSelectors = [
      'i[data-hook="cmps-review-star-rating"] span.a-icon-alt',
      'i[data-hook="review-star-rating"] span.a-icon-alt',
      '.a-icon-star span.a-icon-alt',
      '[class*="star"] span'
    ];
    
    for (const selector of starSelectors) {
      const starEl = block.querySelector(selector);
      if (starEl) {
        const starText = starEl.textContent.trim();
        const match = starText.match(/(\d+(?:\.\d+)?)/);
        if (match) {
          return {
            star: parseFloat(match[1]),
            starText: starText
          };
        }
      }
    }
    
    return { star: null, starText: '' };
  }

  // 获取评论正文
  function getReviewBody(block) {
    const bodySelectors = [
      '[data-hook="review-body"]',
      '.review-text',
      '.review-text-content', // 新增：匹配新的评论内容结构
      '.cr-original-review-text'
    ];
    
    for (const selector of bodySelectors) {
      const bodyNode = block.querySelector(selector);
      if (bodyNode) {
        const spans = bodyNode.querySelectorAll('span');
        if (spans.length > 0) {
          const parts = Array.from(spans)
            .map(s => s.textContent.trim())
            .filter(t => t && t.length > 0);
          return [...new Set(parts)].join('\n');
        }
        return bodyNode.textContent.trim();
      }
    }
    return '';
  }

  // 调试页面结构
  function debugPageStructure() {
    log('🔍 调试模式：分析页面结构...');

    // 查找可能的评论容器
    const possibleContainers = [
      'div[id*="review"]',
      'div[id*="customer"]',
      'div.a-section',
      'div[data-hook*="review"]',
      'div.celwidget'
    ];

    possibleContainers.forEach(selector => {
      const elements = document.querySelectorAll(selector);
      if (elements.length > 0) {
        log(`📍 找到 ${elements.length} 个 "${selector}" 元素`);
        // 显示前几个元素的ID和类名
        Array.from(elements).slice(0, 3).forEach((el, index) => {
          const id = el.id || '无ID';
          const className = el.className || '无类名';
          log(`  - 元素${index + 1}: ID="${id}", Class="${className}"`);
        });
      }
    });

    // 查找包含用户名的元素
    const nameElements = document.querySelectorAll('.a-profile-name, [data-hook="review-author"]');
    if (nameElements.length > 0) {
      log(`👤 找到 ${nameElements.length} 个用户名元素`);
    }

    // 查找包含评分的元素
    const ratingElements = document.querySelectorAll('[data-hook*="star"], .a-icon-star');
    if (ratingElements.length > 0) {
      log(`⭐ 找到 ${ratingElements.length} 个评分元素`);
    }
  }

  // 更新当前页码
  function updateCurrentPage() {
    const urlPageNumber = getQueryParam('pageNumber');
    if (urlPageNumber) {
      const newPage = parseInt(urlPageNumber, 10);
      if (newPage !== currentPage) {
        currentPage = newPage;
        log(`📄 页码已更新为: ${currentPage}`);
        updateUI(); // 更新UI显示
      }
    }
  }

  /**************** 自动翻页循环 ****************/
  function autoLoop() {
    if (!autoMode) {
      log('⏹️ 自动模式已停止。');
      return;
    }

    log(`🔄 开始自动循环 - 当前页: ${currentPage}, 已处理页数: ${totalTriedPages}/${CONFIG.maxAutoPages}`);

    // 先采集当前页
    collectCurrentPageWithRetry();
    totalTriedPages++;

    if (totalTriedPages >= CONFIG.maxAutoPages) {
      log(`🏁 达到最大自动页数 ${CONFIG.maxAutoPages}，停止。`);
      autoMode = false;
      clearAutoModeState(); // 清除状态
      return;
    }

    // 查找下一页按钮
    const nextButton = findNextPageButton();
    if (!nextButton) {
      log('🏁 未找到下一页按钮，自动采集结束。');
      autoMode = false;
      clearAutoModeState(); // 清除状态
      return;
    }

    const delay = CONFIG.delayBase + Math.random() * CONFIG.delayJitter;
    log(`⏭️ 准备点击下一页按钮，延迟 ${Math.round(delay)} ms ...`);

    setTimeout(() => {
      if (!autoMode) {
        log('⏹️ 翻页前检测到停止信号，中止。');
        return;
      }

      // 保存自动模式状态到localStorage，以便页面跳转后恢复
      localStorage.setItem('amazonReviewScraperAutoMode', 'true');
      localStorage.setItem('amazonReviewScraperTotalPages', totalTriedPages.toString());
      localStorage.setItem('amazonReviewScraperMaxPages', CONFIG.maxAutoPages.toString());
      localStorage.setItem('amazonReviewScraperClickTime', Date.now().toString());

      log('🖱️ 模拟点击下一页按钮...');
      log(`📍 按钮URL: ${nextButton.href}`);

      try {
        // 直接点击按钮
        nextButton.click();
        log('✅ 下一页按钮点击成功，等待页面跳转...');
      } catch (error) {
        log(`❌ 点击下一页按钮失败: ${error.message}`);
        autoMode = false;
        clearAutoModeState();
      }
    }, delay);
  }

  /**************** 工具函数 ****************/
  function text(el) {
    return el ? el.textContent.trim() : '';
  }

  function parseHelpful(raw) {
    if (!raw) return 0;
    let num = 0;
    const onePattern = /\bOne\b/i;
    const digits = raw.match(/([\d,\.]+)/);
    if (digits) {
      num = parseInt(digits[1].replace(/[,\.\s]/g, ''), 10);
    } else if (onePattern.test(raw)) {
      num = 1;
    }
    return isNaN(num) ? 0 : num;
  }

  function tryParseDate(dateStr) {
    if (!dateStr) return '';
    let candidate = dateStr;
    const onIdx = dateStr.toLowerCase().lastIndexOf(' on ');
    if (onIdx !== -1) {
      candidate = dateStr.slice(onIdx + 4).trim();
    }
    const d = new Date(candidate);
    return !isNaN(d.getTime()) ? d.toISOString() : '';
  }

  function getQueryParam(key) {
    const url = new URL(location.href);
    return url.searchParams.get(key);
  }

  // 查找下一页按钮（用于点击）
  function findNextPageButton() {
    // 首先检查是否已经是最后一页
    if (isLastPage()) {
      log('📄 已到达最后一页，没有下一页按钮');
      return null;
    }

    // 1. 优先查找包含"Next page"文本的链接
    const allLinks = document.querySelectorAll('a[href*="pageNumber"]');
    log(`🔍 找到 ${allLinks.length} 个包含pageNumber的链接`);

    for (const link of allLinks) {
      const linkText = link.textContent.trim().toLowerCase();
      log(`🔗 检查链接: "${linkText}" -> ${link.href}`);

      // 检查是否为下一页按钮
      if (linkText.includes('next page') || linkText.includes('next')) {
        // 验证按钮是否有效（不是置灰状态）
        if (isButtonDisabled(link)) {
          log(`⚠️ "Next page"按钮已置灰，无法点击`);
          return null;
        }

        // 验证按钮指向的页码是否正确
        const targetPage = extractPageFromURL(link.href);
        if (targetPage && targetPage > currentPage) {
          log(`✅ 找到有效的"Next page"按钮，指向第${targetPage}页: ${link.href}`);
          return link;
        } else {
          log(`⚠️ "Next page"按钮指向页码不正确: 当前${currentPage}页，按钮指向${targetPage}页`);
        }
      }
    }

    // 2. 使用传统选择器
    const nextSelectors = [
      'li.a-last a:not(.a-disabled)',
      '[aria-label*="Next"]:not(.a-disabled)',
      '.a-pagination .a-last a:not(.a-disabled)',
      'a[aria-label*="next page"]:not(.a-disabled)'
    ];

    for (const selector of nextSelectors) {
      const nextButton = document.querySelector(selector);
      if (nextButton && nextButton.href && !isButtonDisabled(nextButton)) {
        const targetPage = extractPageFromURL(nextButton.href);
        if (targetPage && targetPage > currentPage) {
          log(`🔗 找到有效的下一页按钮: ${selector}，指向第${targetPage}页`);
          return nextButton;
        }
      }
    }

    log('❌ 未找到有效的下一页按钮');
    return null;
  }

  // 检查是否为最后一页
  function isLastPage() {
    // 方法1：检查页面信息
    const pageInfo = document.querySelector('[data-hook="cr-filter-info-review-rating-count"]');
    if (pageInfo) {
      const text = pageInfo.textContent;
      const match = text.match(/(\d+)\s*-\s*(\d+)\s*of\s*(\d+)/);
      if (match) {
        const [, , end, total] = match.map(Number);
        if (end >= total) {
          log('📄 根据页面信息判断：已到达最后一页');
          return true;
        }
      }
    }

    // 方法2：检查是否存在置灰的下一页按钮
    const disabledNextButtons = document.querySelectorAll('li.a-last.a-disabled, .a-pagination .a-last.a-disabled, [aria-label*="Next"].a-disabled');
    if (disabledNextButtons.length > 0) {
      log('📄 根据置灰按钮判断：已到达最后一页');
      return true;
    }

    // 方法3：检查是否只有一页
    const paginationElements = document.querySelectorAll('.a-pagination li, .a-pagination a');
    if (paginationElements.length <= 2) { // 通常只有"Previous"和当前页
      log('📄 根据分页元素判断：只有一页');
      return true;
    }

    return false;
  }

  // 检查按钮是否被禁用
  function isButtonDisabled(button) {
    return button.classList.contains('a-disabled') ||
           button.parentElement?.classList.contains('a-disabled') ||
           button.getAttribute('aria-disabled') === 'true' ||
           button.style.pointerEvents === 'none';
  }

  // 从URL中提取页码
  function extractPageFromURL(url) {
    try {
      const urlObj = new URL(url);
      const pageNumber = urlObj.searchParams.get('pageNumber');
      return pageNumber ? parseInt(pageNumber, 10) : null;
    } catch (error) {
      return null;
    }
  }

  function buildNextPageUrl() {
    // 首先尝试找到下一页按钮
    const nextButton = findNextPageButton();
    if (nextButton) {
      return nextButton.href;
    }

    // 如果没有找到按钮，尝试构造URL
    log('⚠️ 未找到下一页按钮，尝试构造URL');

    const url = new URL(location.href);
    const current = parseInt(url.searchParams.get('pageNumber') || '1', 10);
    const nextPage = current + 1;

    // 检查是否已经是最后一页
    const pageInfo = document.querySelector('[data-hook="cr-filter-info-review-rating-count"]');
    if (pageInfo) {
      const text = pageInfo.textContent;
      const match = text.match(/(\d+)\s*-\s*(\d+)\s*of\s*(\d+)/);
      if (match) {
        const [, , end, total] = match.map(Number);
        if (end >= total) {
          log('📄 已到达最后一页');
          return null;
        }
      }
    }

    // 尝试构造与手动点击相同格式的URL
    // 提取产品ID (ASIN)
    const pathMatch = url.pathname.match(/\/product-reviews\/([A-Z0-9]{10})/);
    if (pathMatch) {
      const asin = pathMatch[1];
      const baseUrl = `${url.origin}/product-reviews/${asin}/ref=cm_cr_getr_d_paging_btm_next_${nextPage}`;

      // 构造查询参数
      const params = new URLSearchParams();
      params.set('ie', 'UTF8');
      if (url.searchParams.get('filterByStar')) {
        params.set('filterByStar', url.searchParams.get('filterByStar'));
      }
      params.set('reviewerType', url.searchParams.get('reviewerType') || 'all_reviews');
      params.set('pageNumber', nextPage.toString());

      const constructedUrl = `${baseUrl}?${params.toString()}#reviews-filter-bar`;
      log(`🔧 构造的URL: ${constructedUrl}`);
      return constructedUrl;
    }

    // 如果无法提取ASIN，使用原来的方法
    url.searchParams.set('pageNumber', nextPage.toString());
    return url.toString();
  }

  function detectCaptcha() {
    // 检查特定的验证码元素（更精确的检测）
    const captchaSelectors = [
      'form[action*="validateCaptcha"]',
      '#captchacharacters',
      '.cvf-widget-form',
      '[name="captcha"]',
      'input[name="field-keywords"][placeholder*="characters"]'
    ];

    for (const selector of captchaSelectors) {
      if (document.querySelector(selector)) {
        log(`🚫 检测到验证码元素: ${selector}`);
        return true;
      }
    }

    // 检查页面标题和特定文本（更精确的文本检测）
    const title = document.title.toLowerCase();
    const bodyText = document.body.innerText.toLowerCase();

    // 只检查明确的验证码相关文本
    const preciseCaptchaKeywords = [
      'enter the characters you see below',
      'type the characters you see in this image',
      'automated access',
      'unusual traffic',
      'verify you are human'
    ];

    for (const keyword of preciseCaptchaKeywords) {
      if (bodyText.includes(keyword.toLowerCase()) || title.includes(keyword.toLowerCase())) {
        log(`🚫 检测到反爬虫标识: ${keyword}`);
        return true;
      }
    }

    return false;
  }

  function updateUI() {
    countSpan.textContent = collected.length;
    pageSpan.textContent = currentPage;
  }

  function getTimestamp() {
    return new Date().toISOString().slice(0, 19).replace(/[:-]/g, '');
  }

  function downloadFile(dataStr, filename, mime) {
    const blob = new Blob([dataStr], { type: mime });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.style.display = 'none';
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    setTimeout(() => {
      URL.revokeObjectURL(url);
      a.remove();
    }, 200);
    log(`📥 已触发下载：${filename}`);
  }

  function escapeCSVField(field) {
    if (field == null) return '';
    let s = String(field).replace(/\r?\n/g, ' ');
    if (/[",]/.test(s)) {
      s = '"' + s.replace(/"/g, '""') + '"';
    }
    return s;
  }

  function toCSV(arr) {
    const headers = [
      'reviewId', 'reviewer', 'star', 'starText', 'title',
      'dateRaw', 'dateISO', 'body', 'verified', 'imageCount',
      'helpful', 'pageNumber', 'url', 'scrapedAt'
    ];
    const lines = [headers.join(',')];
    arr.forEach(o => {
      const row = headers.map(h => escapeCSVField(o[h]));
      lines.push(row.join(','));
    });
    return lines.join('\n');
  }

  function log(msg) {
    const time = new Date().toLocaleTimeString();
    const line = `[${time}] ${msg}`;
    const div = document.createElement('div');
    div.textContent = line;
    div.style.cssText = 'margin-bottom: 2px; padding: 2px 0; border-bottom: 1px solid #333;';
    logBox.appendChild(div);

    // 限制行数
    while (logBox.children.length > CONFIG.logLimit) {
      logBox.removeChild(logBox.firstChild);
    }
    logBox.scrollTop = logBox.scrollHeight;
    console.log('[ReviewScraper]', msg);
  }

  /**************** 数据持久化 ****************/
  function initializeData() {
    try {
      const savedData = GM_getValue('collected_reviews', '[]');
      const savedIds = GM_getValue('collected_ids', '[]');

      if (savedData && savedData !== '[]') {
        collected = JSON.parse(savedData);
        collectedIds = new Set(JSON.parse(savedIds));
        updateUI();
        log(`📂 从本地存储恢复了 ${collected.length} 条评论数据`);
      }
    } catch (error) {
      log(`⚠️ 恢复数据失败: ${error.message}`);
    }
  }

  function saveData() {
    try {
      GM_setValue('collected_reviews', JSON.stringify(collected));
      GM_setValue('collected_ids', JSON.stringify([...collectedIds]));
    } catch (error) {
      log(`⚠️ 保存数据失败: ${error.message}`);
    }
  }

  // 定期保存数据
  setInterval(saveData, 30000); // 每30秒保存一次

  // 页面卸载时保存数据
  window.addEventListener('beforeunload', saveData);

  /**************** 启动脚本 ****************/
  initializeData();
  updateUI();

  // 检查是否需要恢复自动模式
  function checkAutoModeResume() {
    log('🔍 检查自动模式恢复状态...');

    const shouldResume = localStorage.getItem('amazonReviewScraperAutoMode');
    const savedTotalPages = parseInt(localStorage.getItem('amazonReviewScraperTotalPages') || '0', 10);
    const savedMaxPages = parseInt(localStorage.getItem('amazonReviewScraperMaxPages') || '20', 10);
    const clickTime = parseInt(localStorage.getItem('amazonReviewScraperClickTime') || '0', 10);

    log(`📊 localStorage状态: shouldResume=${shouldResume}, savedTotalPages=${savedTotalPages}, savedMaxPages=${savedMaxPages}, clickTime=${clickTime}`);

    if (shouldResume === 'true') {
      // 检查点击时间，如果超过120秒说明可能卡住了
      const now = Date.now();
      const timeDiff = now - clickTime;
      log(`⏱️ 点击时间差: ${timeDiff}ms (${Math.round(timeDiff/1000)}秒)`);

      if (clickTime && timeDiff > 120000) { // 120秒超时
        log('⚠️ 检测到可能的翻页超时，清除自动模式状态');
        clearAutoModeState();
        return;
      }

      // 清除标记
      clearAutoModeState();

      // 恢复自动模式
      autoMode = true;
      totalTriedPages = savedTotalPages;
      CONFIG.maxAutoPages = savedMaxPages;

      log(`🔄 恢复自动采集模式，已处理 ${totalTriedPages} 页，最大页数 ${CONFIG.maxAutoPages}`);
      updateUI(); // 更新UI显示自动模式状态

      // 等待页面完全加载后再开始
      log('⏳ 等待页面完全加载后开始恢复的自动采集...');
      waitForPageReady(() => {
        if (autoMode) {
          log('▶️ 页面已就绪，开始恢复的自动采集循环...');
          autoLoop();
        }
      });
    } else {
      log('ℹ️ 无需恢复自动模式');
    }
  }

  // 等待页面就绪的函数
  function waitForPageReady(callback) {
    let attempts = 0;
    const maxAttempts = 20; // 最多等待20次，每次500ms

    function checkReady() {
      attempts++;

      // 检查页面是否有评论元素
      const reviewItems = getReviewItems();
      const hasReviews = reviewItems.length > 0;

      // 检查页面是否有分页元素
      const hasPagination = document.querySelector('.a-pagination') ||
                           document.querySelector('[data-hook="cr-filter-info-review-rating-count"]');

      log(`🔍 页面就绪检查 ${attempts}/${maxAttempts}: 评论=${reviewItems.length}个, 分页=${!!hasPagination}`);

      if (hasReviews && hasPagination) {
        log('✅ 页面已就绪，包含评论和分页元素');
        callback();
        return;
      }

      if (attempts >= maxAttempts) {
        log('⚠️ 页面就绪检查超时，强制开始');
        callback();
        return;
      }

      // 继续等待
      setTimeout(checkReady, 500);
    }

    checkReady();
  }

  // 清除自动模式状态的辅助函数
  function clearAutoModeState() {
    localStorage.removeItem('amazonReviewScraperAutoMode');
    localStorage.removeItem('amazonReviewScraperTotalPages');
    localStorage.removeItem('amazonReviewScraperMaxPages');
    localStorage.removeItem('amazonReviewScraperClickTime');
    log('🧹 已清除localStorage中的自动模式状态');
  }

  log(`🚀 脚本已加载。当前页 pageNumber=${currentPage}。随时点击"采集当前页"或"自动采集"。`);
  log(`⚙️ 配置：最大页数=${CONFIG.maxAutoPages}, 基础延迟=${CONFIG.delayBase}ms`);

  // 多种方式确保自动模式恢复

  // 方式1: DOMContentLoaded
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
      setTimeout(checkAutoModeResume, 500);
    });
  } else {
    setTimeout(checkAutoModeResume, 500);
  }

  // 方式2: window.onload
  window.addEventListener('load', () => {
    setTimeout(() => {
      const shouldResume = localStorage.getItem('amazonReviewScraperAutoMode');
      if (shouldResume === 'true' && !autoMode) {
        log('🔄 window.onload检查：发现需要恢复的自动模式');
        checkAutoModeResume();
      }
    }, 1000);
  });

  // 方式3: 定时检查（前10秒内每秒检查一次）
  let checkCount = 0;
  const intervalId = setInterval(() => {
    checkCount++;
    const shouldResume = localStorage.getItem('amazonReviewScraperAutoMode');
    if (shouldResume === 'true' && !autoMode) {
      log(`🔄 定时检查${checkCount}：发现需要恢复的自动模式`);
      checkAutoModeResume();
      clearInterval(intervalId);
    } else if (checkCount >= 10) {
      clearInterval(intervalId);
    }
  }, 1000);

})();
